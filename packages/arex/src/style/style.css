@import 'allotment/dist/style.css';

:root{
  --jse-font-size: 14px;
  --jse-padding: 4px 8px;
}

body {
  overflow: hidden;
}



#root,
#root > .ant-app,
#root > .ant-app > .ant-layout,
#root > .ant-app > .ant-layout > .ant-layout-content {
  height: 100vh;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: #88888844 !important;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: #88888844 !important;
}

::-webkit-scrollbar-corner {
  background-color: transparent !important;
}

/* ArexMenuContainer 样式 */
#arex-menu-content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  overflow-y: hidden;
}

#arex-menu-content-wrapper::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

#arex-menu-content-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

#arex-menu-content-wrapper::-webkit-scrollbar-thumb {
  background: #88888844;
  border-radius: 3px;
}

#arex-menu-content-wrapper::-webkit-scrollbar-thumb:hover {
  background: #888888aa;
}

