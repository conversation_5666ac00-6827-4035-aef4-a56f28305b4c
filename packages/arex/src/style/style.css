@import 'allotment/dist/style.css';

:root{
  --jse-font-size: 14px;
  --jse-padding: 4px 8px;
}

body {
  overflow: hidden;
}



#root,
#root > .ant-app,
#root > .ant-app > .ant-layout,
#root > .ant-app > .ant-layout > .ant-layout-content {
  height: 100vh;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: #88888844 !important;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: #88888844 !important;
}

::-webkit-scrollbar-corner {
  background-color: transparent !important;
}

/* ArexMenuContainer 样式 */
#arex-menu-content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 禁用 Ant Design Tree 组件的内置滚动 */
#arex-menu-content-wrapper .ant-tree {
  overflow: visible !important;
}

#arex-menu-content-wrapper .ant-tree-list {
  overflow: visible !important;
}

#arex-menu-content-wrapper .ant-tree-list-holder {
  overflow: visible !important;
}

#arex-menu-content-wrapper .ant-tree-list-holder-inner {
  overflow: visible !important;
}

/* 隐藏 Tree 组件可能的滚动条 */
#arex-menu-content-wrapper .ant-tree::-webkit-scrollbar,
#arex-menu-content-wrapper .ant-tree-list::-webkit-scrollbar,
#arex-menu-content-wrapper .ant-tree-list-holder::-webkit-scrollbar {
  display: none !important;
}

